package com.mapleisle.reflect;

import java.lang.reflect.Method;
import java.util.Properties;

public class ReflectDemo1 {
    public static void main(String[] args) throws Exception {
        // 读取 properties 文件 1
        Properties properties = new Properties();
        properties.load(ReflectDemo1.class.getClassLoader().getResourceAsStream("re.properties"));
        String classfullpath1 = properties.getProperty("classfullpath");
        String methodname = properties.getProperty("methodname");
        System.out.println(classfullpath1);
        System.out.println(methodname);
        // 创建对象
        Class<?> class1 = Class.forName(classfullpath1);
        Object obj = class1.newInstance();
        // 获取方法并且执行
        class1.getMethod(methodname).invoke(obj);
        Method hi = class1.getDeclaredMethod(methodname);
        hi.invoke(obj);
    }

    /**
     * 功能描述：
     *
     * <AUTHOR>
     * @date 2025-08-12 15:31:54
     */
    public void hi() {
        System.out.println("Hi~");
    }
}
